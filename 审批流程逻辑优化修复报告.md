# 审批流程逻辑优化修复报告

## 修改概述

根据需求对审批流程的审批人确定逻辑进行了全面优化，实现了四种不同的审批人类型逻辑。

## 审批人类型定义

| 类型值 | 类型名称 | 逻辑说明 |
|--------|----------|----------|
| "1" | 特定用户 | 需要在前端指定具体的审批人员 |
| "2" | 部门负责人 | 根据申请人所在部门的负责人(principalId)审核 |
| "3" | 分管领导 | 根据申请人所在部门的分管领导(managerId)审核 |
| "4" | 综合办公室 | 指定部门编号为ZHBGS的部门负责人审核 |

## 前端修改

### 1. 条件判断优化
```vue
<!-- 修改前：显示多种类型的配置 -->
<div v-if="step.approverType === '1' || step.approverType === '4'">

<!-- 修改后：只有指定人员需要配置 -->
<div v-if="step.approverType === '1'">
```

### 2. 删除不必要的配置项
- **删除角色选择**：移除了审批角色的选择表单项
- **简化配置**：只保留指定人员的用户选择功能

### 3. 保存逻辑优化
```javascript
// 修改前：处理多种类型的配置
if (newStep.approverType === '1' && newStep.approverUserIds) {
  // 指定人员配置
} else if (newStep.approverType === '4' && newStep.approverRoleIds) {
  // 角色配置
}

// 修改后：只处理指定人员
if (newStep.approverType === '1' && newStep.approverUserIds) {
  newStep.approverConfig = JSON.stringify({
    userIds: newStep.approverUserIds,
  });
}
// 其他类型(2,3,4)由后端根据申请人动态确定审批人
```

## 后端修改

### 1. 审批人确定逻辑调整

#### ApproverRuleEngineImpl.determineApprovers() 方法
```java
switch (approverType) {
    case 1: // 指定人员
        approverIds = getSpecifiedApprovers(approverConfig);
        break;
    case 2: // 部门负责人
        Long deptManager = getDepartmentManagerByUserId(applicantId);
        if (deptManager != null) {
            approverIds.add(deptManager);
        }
        break;
    case 3: // 分管领导
        Long deptLeader = getDepartmentLeaderByUserId(applicantId);
        if (deptLeader != null) {
            approverIds.add(deptLeader);
        }
        break;
    case 4: // 综合办公室
        approverIds = getAdminOfficeApprovers();
        break;
}
```

### 2. 综合办公室审批人获取逻辑
```java
private List<Long> getAdminOfficeApprovers() {
    try {
        // 根据部门编号 ZHBGS 获取综合办公室部门负责人
        Dept adminOfficeDept = deptMapper.selectOne(
            new LambdaQueryWrapper<Dept>()
                .eq(Dept::getDeptCode, "ZHBGS")
                .eq(Dept::getIsDeleted, 0)
        );
        
        if (adminOfficeDept != null && adminOfficeDept.getPrincipalId() != null) {
            return List.of(adminOfficeDept.getPrincipalId());
        }
        
        return Collections.emptyList();
    } catch (Exception e) {
        log.error("获取综合办公室审批人时发生异常", e);
        return Collections.emptyList();
    }
}
```

### 3. 部门相关字段映射
- **principalId**: 部门负责人ID (对应 sys_dept.principal_id)
- **managerId**: 分管领导ID (对应 sys_dept.manager_id)

## 数据流程

### 1. 指定人员 (类型1)
```
前端选择具体用户 → approverConfig: {"userIds":[1,2,3]}
    ↓
后端解析配置 → 返回指定的用户ID列表
```

### 2. 部门负责人 (类型2)
```
获取申请人信息 → user.deptId
    ↓
查询部门信息 → dept.principalId
    ↓
返回部门负责人ID
```

### 3. 分管领导 (类型3)
```
获取申请人信息 → user.deptId
    ↓
查询部门信息 → dept.managerId
    ↓
返回分管领导ID
```

### 4. 综合办公室 (类型4)
```
查询部门编号为ZHBGS的部门 → dept.deptCode = "ZHBGS"
    ↓
获取该部门负责人 → dept.principalId
    ↓
返回综合办公室负责人ID
```

## 数据库字段说明

### sys_dept 表字段
```sql
CREATE TABLE sys_dept (
    id BIGINT PRIMARY KEY,
    dept_code VARCHAR(50),      -- 部门编码
    dept_name VARCHAR(100),     -- 部门名称
    principal_id BIGINT,        -- 部门负责人ID
    manager_id BIGINT,          -- 分管领导ID
    is_deleted TINYINT DEFAULT 0
);
```

### 关键字段映射
- `principal_id` → `Dept.principalId` → 部门负责人
- `manager_id` → `Dept.managerId` → 分管领导
- `dept_code = "ZHBGS"` → 综合办公室部门

## 用户界面变化

### 1. 审批流程配置页面
- **简化配置**：只有选择"特定用户"时才显示人员选择框
- **自动化处理**：其他类型无需前端配置，由后端自动确定

### 2. 审批人类型选项
```
1 - 特定用户     [需要选择具体人员]
2 - 部门负责人   [自动确定]
3 - 分管领导     [自动确定]
4 - 综合办公室   [自动确定]
```

## 错误处理

### 1. 前端错误处理
- **数据验证**：只有选择"特定用户"时才验证是否选择了具体人员
- **界面提示**：其他类型显示"系统自动确定审批人"

### 2. 后端错误处理
```java
// 部门负责人不存在
if (deptManager == null) {
    log.warn("申请人{}所在部门没有配置负责人", applicantId);
    return Collections.emptyList();
}

// 分管领导不存在
if (deptLeader == null) {
    log.warn("申请人{}所在部门没有配置分管领导", applicantId);
    return Collections.emptyList();
}

// 综合办公室部门不存在
if (adminOfficeDept == null) {
    log.warn("未找到综合办公室(ZHBGS)部门");
    return Collections.emptyList();
}
```

## 测试验证

### 1. 功能测试
1. **指定人员测试**：
   - 选择审批人类型为"特定用户"
   - 选择具体的审批人员
   - 保存并验证配置正确

2. **部门负责人测试**：
   - 选择审批人类型为"部门负责人"
   - 确认不显示人员选择框
   - 提交请假申请，验证审批人为申请人部门负责人

3. **分管领导测试**：
   - 选择审批人类型为"分管领导"
   - 提交请假申请，验证审批人为申请人部门分管领导

4. **综合办公室测试**：
   - 选择审批人类型为"综合办公室"
   - 提交请假申请，验证审批人为ZHBGS部门负责人

### 2. 数据验证
```sql
-- 验证部门数据完整性
SELECT 
    dept_code,
    dept_name,
    principal_id,
    manager_id
FROM sys_dept 
WHERE is_deleted = 0;

-- 验证综合办公室部门
SELECT * FROM sys_dept WHERE dept_code = 'ZHBGS';
```

## 部署注意事项

### 1. 数据准备
确保以下数据完整：
- 所有部门都配置了 `principal_id`（部门负责人）
- 需要分管领导审批的部门配置了 `manager_id`
- 存在部门编码为 "ZHBGS" 的综合办公室部门

### 2. 配置检查
```sql
-- 检查缺少部门负责人的部门
SELECT dept_code, dept_name FROM sys_dept 
WHERE principal_id IS NULL AND is_deleted = 0;

-- 检查缺少分管领导的部门
SELECT dept_code, dept_name FROM sys_dept 
WHERE manager_id IS NULL AND is_deleted = 0;
```

## 总结

### ✅ 优化效果
1. **简化配置**：前端配置更加简洁，只有指定人员需要手动选择
2. **自动化处理**：大部分审批人由系统根据组织架构自动确定
3. **逻辑清晰**：四种审批人类型逻辑明确，易于理解和维护
4. **灵活性强**：支持不同场景的审批需求

### ✅ 技术改进
1. **前后端分离**：前端专注于界面交互，后端处理业务逻辑
2. **代码简化**：减少了不必要的配置项和复杂判断
3. **可维护性**：逻辑集中在后端，便于统一管理和修改
4. **扩展性**：易于添加新的审批人类型

现在审批流程的逻辑更加清晰和自动化，大大提升了用户体验和系统的可维护性！
