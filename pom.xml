<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.rongmei</groupId>
    <artifactId>rongmei-platform-root</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>rongmei-platform-root</name>
    <description>融媒体平台根项目</description>

    <modules>
        <module>backend</module>
        <!-- frontend为Node.js项目，通过frontend-maven-plugin插件进行管理 -->
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <frontend.maven.plugin.version>1.12.1</frontend.maven.plugin.version>
        <node.version>v18.12.1</node.version>
        <pnpm.version>8.15.0</pnpm.version>
    </properties>

    <build>
        <plugins>
            <!-- 管理前端项目构建的插件 -->
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>${frontend.maven.plugin.version}</version>
                <configuration>
                    <workingDirectory>frontend</workingDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>install-frontend-tools</id>
                        <goals>
                            <goal>install-node-and-pnpm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>${node.version}</nodeVersion>
                            <pnpmVersion>${pnpm.version}</pnpmVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>pnpm-install</id>
                        <goals>
                            <goal>pnpm</goal>
                        </goals>
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>build-frontend</id>
                        <goals>
                            <goal>pnpm</goal>
                        </goals>
                        <phase>prepare-package</phase>
                        <configuration>
                            <arguments>build</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>