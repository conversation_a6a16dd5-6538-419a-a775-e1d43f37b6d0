# 流程唯一性约束修复报告

## 修改概述

实现了审批流程的唯一性约束，确保请假类型+人员类型的组合在系统中唯一，并且获取流程时也基于这两个条件进行匹配。

## 唯一性规则

### 约束条件
- **请假类型 + 人员类型** 的组合必须唯一
- 同一组合只能存在一个启用的审批流程
- 编辑时排除当前记录进行唯一性检查

### 数据来源
- **请假类型**：从前端选择的请假类型ID列表
- **人员类型**：从用户表(sys_user.personnel_type)获取

## 前端修改

### 1. 表单提交流程优化
```javascript
// 修改前：直接提交表单
function handleSubmit() {
  formRef.value.validate((valid) => {
    if (valid) {
      // 直接提交...
    }
  });
}

// 修改后：增加唯一性检查
async function handleSubmit() {
  // 1. 表单验证
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;

  // 2. 唯一性检查
  const uniqueCheckResult = await checkWorkflowUniqueness();
  if (!uniqueCheckResult.success) {
    ElMessage.error(uniqueCheckResult.message);
    return;
  }

  // 3. 提交表单
  try {
    // 处理数据并提交...
  } catch (error) {
    ElMessage.error("保存失败，请重试");
  }
}
```

### 2. 唯一性检查方法
```javascript
async function checkWorkflowUniqueness() {
  try {
    const checkParams = {
      leaveTypeIds: form.leaveTypeIds,    // 请假类型ID列表
      personnelType: form.personnelType,  // 人员类型
      excludeId: form.id                  // 编辑时排除当前记录
    };

    const response = await checkWorkflowUniquenessAPI(checkParams);
    return response.data || { success: true };
  } catch (error) {
    return { 
      success: false, 
      message: "检查流程唯一性失败，请重试" 
    };
  }
}
```

### 3. API接口添加
```javascript
// frontend/src/api/approval/workflow.js
export function checkWorkflowUniqueness(params) {
  return request({
    url: `${APPROVAL_WORKFLOW_BASE_URL}/check-uniqueness`,
    method: "post",
    data: params,
  });
}
```

## 后端修改

### 1. Controller接口
```java
@Operation(summary = "检查流程唯一性")
@PostMapping("/check-uniqueness")
public Result<Map<String, Object>> checkUniqueness(@RequestBody Map<String, Object> params) {
    boolean isUnique = approvalWorkflowService.checkUniqueness(params);
    Map<String, Object> result = new HashMap<>();
    result.put("success", isUnique);
    if (!isUnique) {
        result.put("message", "该请假类型和人员类型的组合已存在流程配置");
    }
    return Result.success(result);
}
```

### 2. Service接口
```java
/**
 * 检查流程唯一性
 *
 * @param params 检查参数
 * @return 是否唯一
 */
boolean checkUniqueness(Map<String, Object> params);
```

### 3. Service实现
```java
@Override
public boolean checkUniqueness(Map<String, Object> params) {
    try {
        String leaveTypeIds = (String) params.get("leaveTypeIds");
        String personnelType = (String) params.get("personnelType");
        Long excludeId = params.get("excludeId") != null ? 
            Long.valueOf(params.get("excludeId").toString()) : null;

        // 构建查询条件
        LambdaQueryWrapper<ApprovalWorkflow> wrapper = new LambdaQueryWrapper<>();
        
        // 处理请假类型ID条件
        if (leaveTypeIds != null && !leaveTypeIds.trim().isEmpty()) {
            // 转换为JSON数组格式进行匹配
            String[] typeIds = leaveTypeIds.split(",");
            StringBuilder jsonArray = new StringBuilder("[");
            for (int i = 0; i < typeIds.length; i++) {
                if (i > 0) jsonArray.append(",");
                jsonArray.append(typeIds[i].trim());
            }
            jsonArray.append("]");
            wrapper.eq(ApprovalWorkflow::getLeaveTypeIds, jsonArray.toString());
        } else {
            // 空值匹配通用流程
            wrapper.and(w -> w.eq(ApprovalWorkflow::getLeaveTypeIds, "[]")
                .or().isNull(ApprovalWorkflow::getLeaveTypeIds)
                .or().eq(ApprovalWorkflow::getLeaveTypeIds, ""));
        }
        
        // 人员类型条件
        if (personnelType != null && !personnelType.trim().isEmpty()) {
            wrapper.eq(ApprovalWorkflow::getPersonnelType, personnelType);
        } else {
            wrapper.and(w -> w.isNull(ApprovalWorkflow::getPersonnelType)
                .or().eq(ApprovalWorkflow::getPersonnelType, ""));
        }
        
        // 排除当前编辑的记录
        if (excludeId != null) {
            wrapper.ne(ApprovalWorkflow::getId, excludeId);
        }
        
        // 只检查启用的流程
        wrapper.eq(ApprovalWorkflow::getStatus, 1);
        wrapper.eq(ApprovalWorkflow::getIsDeleted, 0);

        long count = this.count(wrapper);
        return count == 0;
        
    } catch (Exception e) {
        log.error("检查流程唯一性时发生异常", e);
        return false; // 异常时返回false，阻止保存
    }
}
```

## 数据匹配逻辑

### 1. 请假类型匹配
```java
// 前端传递：leaveTypeIds = "1,2,3"
// 转换为：jsonArray = "[1,2,3]"
// 数据库匹配：leave_type_ids = "[1,2,3]"

// 通用流程匹配
// 前端传递：leaveTypeIds = null 或 ""
// 数据库匹配：leave_type_ids = "[]" 或 NULL 或 ""
```

### 2. 人员类型匹配
```java
// 精确匹配
wrapper.eq(ApprovalWorkflow::getPersonnelType, personnelType);

// 通用类型匹配
wrapper.and(w -> w.isNull(ApprovalWorkflow::getPersonnelType)
    .or().eq(ApprovalWorkflow::getPersonnelType, ""));
```

### 3. 编辑时排除当前记录
```java
if (excludeId != null) {
    wrapper.ne(ApprovalWorkflow::getId, excludeId);
}
```

## 流程获取逻辑

### 根据用户信息获取流程
```java
@Override
public ApprovalWorkflow selectWorkflowForApplication(Long applicantId, String requestType, LocalDateTime startTime) {
    // 1. 获取申请人信息
    User applicant = userMapper.selectById(applicantId);
    
    // 2. 从用户表获取人员类型
    String personnelType = applicant.getPersonnelType();
    
    // 3. 根据请假类型+人员类型查找匹配的流程
    ApprovalWorkflow workflow = getMatchingWorkflow(personnelType, deptType, requestType, isWeekend);
    
    // 4. 未找到时使用默认流程
    if (workflow == null) {
        workflow = getDefaultWorkflow();
    }
    
    return workflow;
}
```

## 用户体验优化

### 1. 实时验证
- 用户填写完表单后，点击保存时立即进行唯一性检查
- 检查失败时显示明确的错误提示信息

### 2. 错误提示
```javascript
// 唯一性冲突提示
"该请假类型和人员类型的组合已存在流程配置"

// 检查失败提示
"检查流程唯一性失败，请重试"

// 保存失败提示
"保存失败，请重试"
```

### 3. 编辑模式处理
- 编辑现有流程时，排除当前记录进行唯一性检查
- 允许用户修改其他字段而不触发唯一性冲突

## 数据示例

### 唯一性约束示例
```
流程1: 请假类型=[1,2] + 人员类型=NORMAL ✅ 允许
流程2: 请假类型=[3,4] + 人员类型=NORMAL ✅ 允许
流程3: 请假类型=[1,2] + 人员类型=MIDDLE ✅ 允许
流程4: 请假类型=[1,2] + 人员类型=NORMAL ❌ 冲突（与流程1重复）
```

### 通用流程示例
```
通用流程1: 请假类型=[] + 人员类型=NULL     ✅ 允许
通用流程2: 请假类型=[] + 人员类型=""      ❌ 冲突（与通用流程1重复）
```

## 测试验证

### 1. 新增流程测试
1. **正常新增**：
   - 选择请假类型和人员类型
   - 保存成功

2. **唯一性冲突**：
   - 选择已存在的请假类型+人员类型组合
   - 显示冲突提示，保存失败

### 2. 编辑流程测试
1. **修改其他字段**：
   - 修改流程名称、描述等
   - 保存成功

2. **修改约束字段**：
   - 修改为已存在的组合，显示冲突提示
   - 修改为新组合，保存成功

### 3. 流程获取测试
1. **精确匹配**：
   - 用户的人员类型和请假类型有对应流程
   - 返回匹配的流程

2. **默认流程**：
   - 没有匹配的流程时
   - 返回默认流程

## 部署注意事项

### 1. 数据清理
部署前检查现有数据是否存在冲突：
```sql
-- 检查重复的流程配置
SELECT leave_type_ids, personnel_type, COUNT(*) as count
FROM approval_workflow 
WHERE status = 1 AND is_deleted = 0
GROUP BY leave_type_ids, personnel_type
HAVING COUNT(*) > 1;
```

### 2. 数据迁移
如果存在重复数据，需要：
- 保留最新的流程配置
- 禁用或删除重复的配置
- 更新相关的申请记录

## 总结

### ✅ 实现效果
1. **唯一性保证**：确保请假类型+人员类型组合的唯一性
2. **用户友好**：提供清晰的错误提示和验证反馈
3. **数据一致性**：前后端统一的验证逻辑
4. **编辑支持**：编辑时正确处理唯一性检查

### ✅ 技术优势
1. **前端验证**：提前发现冲突，提升用户体验
2. **后端保障**：数据库层面的最终验证
3. **异常处理**：完善的错误处理机制
4. **扩展性**：易于添加新的约束条件

现在审批流程的新增和编辑功能具备了完善的唯一性约束，确保了数据的一致性和业务逻辑的正确性！
