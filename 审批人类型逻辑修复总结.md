# 审批人类型逻辑修复总结

## 修改概述

已将 WorkflowDialog.vue 中所有使用数字值判断审批人类型的逻辑改为使用字符串值，以匹配字典数据的格式。

## 具体修改内容

### 1. 条件判断修改

#### 审批人配置显示条件
```vue
<!-- 修改前 -->
<div v-if="step.approverType === 1 || step.approverType === 4">

<!-- 修改后 -->
<div v-if="step.approverType === 'SPECIFIC_USER' || step.approverType === 'ROLE_BASED'">
```

#### 指定审批人表单项显示条件
```vue
<!-- 修改前 -->
<el-form-item v-if="step.approverType === 1" label="指定审批人">

<!-- 修改后 -->
<el-form-item v-if="step.approverType === 'SPECIFIC_USER'" label="指定审批人">
```

#### 审批角色表单项显示条件
```vue
<!-- 修改前 -->
<el-form-item v-if="step.approverType === 4" label="审批角色">

<!-- 修改后 -->
<el-form-item v-if="step.approverType === 'ROLE_BASED'" label="审批角色">
```

### 2. 默认值修改

#### 添加步骤时的默认审批人类型
```javascript
// 修改前
form.steps.push({
  stepName: "",
  stepType: 1,
  approverType: 1,  // 数字值
  isRequired: true,
  timeoutHours: 24,
  autoApprove: 0,
});

// 修改后
form.steps.push({
  stepName: "",
  stepType: 1,
  approverType: "SPECIFIC_USER",  // 字符串值
  isRequired: true,
  timeoutHours: 24,
  autoApprove: 0,
});
```

### 3. 保存逻辑修改

#### 审批人配置序列化逻辑
```javascript
// 修改前
if (newStep.approverType === 1 && newStep.approverUserIds) {
  newStep.approverConfig = JSON.stringify({
    userIds: newStep.approverUserIds,
  });
} else if (newStep.approverType === 4 && newStep.approverRoleIds) {
  newStep.approverConfig = JSON.stringify({
    roleIds: newStep.approverRoleIds,
  });
}

// 修改后
if (newStep.approverType === 'SPECIFIC_USER' && newStep.approverUserIds) {
  newStep.approverConfig = JSON.stringify({
    userIds: newStep.approverUserIds,
  });
} else if (newStep.approverType === 'ROLE_BASED' && newStep.approverRoleIds) {
  newStep.approverConfig = JSON.stringify({
    roleIds: newStep.approverRoleIds,
  });
}
```

## 字典值映射关系

### 原数字值 → 新字符串值
```
1 (指定人员) → "SPECIFIC_USER" (指定用户)
2 (直接主管) → "DIRECT_SUPERVISOR" (直接上级)
3 (部门负责人) → "DEPT_MANAGER" (部门负责人)
4 (角色) → "ROLE_BASED" (基于角色)
5 (自定义规则) → 其他字典值...
```

### 当前主要使用的类型
- **SPECIFIC_USER**: 指定用户 - 需要选择具体的审批人
- **ROLE_BASED**: 基于角色 - 需要选择审批角色

## 数据流程验证

### 1. 页面加载流程
```
页面加载 → onMounted()
    ↓
loadApproverTypes() → DictAPI.getDictItems("approver_type")
    ↓
approverTypeOptions.value = 字典数据
    ↓
下拉框显示字典选项
```

### 2. 添加步骤流程
```
点击"添加步骤" → addStep()
    ↓
默认 approverType = "SPECIFIC_USER"
    ↓
显示"指定审批人"配置项
```

### 3. 选择审批人类型流程
```
用户选择审批人类型 → step.approverType = "ROLE_BASED"
    ↓
v-if="step.approverType === 'ROLE_BASED'" 生效
    ↓
显示"审批角色"配置项
```

### 4. 保存流程
```
用户点击保存 → handleSave()
    ↓
检查 approverType === 'SPECIFIC_USER' 或 'ROLE_BASED'
    ↓
序列化对应的配置到 approverConfig
    ↓
提交到后端
```

## 兼容性考虑

### 1. 历史数据处理
如果数据库中存在使用数字值的历史数据，需要进行数据迁移：

```sql
-- 示例迁移脚本
UPDATE approval_step SET approver_type = 'SPECIFIC_USER' WHERE approver_type = '1';
UPDATE approval_step SET approver_type = 'DIRECT_SUPERVISOR' WHERE approver_type = '2';
UPDATE approval_step SET approver_type = 'DEPT_MANAGER' WHERE approver_type = '3';
UPDATE approval_step SET approver_type = 'ROLE_BASED' WHERE approver_type = '4';
```

### 2. 后端接口兼容
确保后端接口能够正确处理字符串类型的 `approverType` 值。

## 测试验证

### 1. 功能测试
1. **页面加载测试**:
   - 打开审批流程配置页面
   - 确认审批人类型下拉框显示字典数据
   - 验证选项值为字符串格式

2. **添加步骤测试**:
   - 点击"添加步骤"
   - 确认默认审批人类型为"指定用户"
   - 验证显示"指定审批人"配置项

3. **切换类型测试**:
   - 选择"基于角色"
   - 确认显示"审批角色"配置项
   - 选择其他类型，确认配置项正确隐藏

4. **保存测试**:
   - 配置完整的审批流程
   - 保存并验证数据正确提交
   - 检查 `approverConfig` 字段正确序列化

### 2. 数据验证
1. **前端数据格式**:
   ```javascript
   // 期望的数据格式
   {
     approverType: "SPECIFIC_USER",  // 字符串值
     approverUserIds: [1, 2, 3],
     approverConfig: '{"userIds":[1,2,3]}'
   }
   ```

2. **后端接收格式**:
   ```json
   {
     "approverType": "SPECIFIC_USER",
     "approverConfig": "{\"userIds\":[1,2,3]}"
   }
   ```

## 错误处理

### 1. 字典数据加载失败
```javascript
function loadApproverTypes() {
  DictAPI.getDictItems("approver_type")
    .then((response) => {
      approverTypeOptions.value = response;
    })
    .catch((error) => {
      console.error("加载审批人类型失败:", error);
      // 设置默认选项
      approverTypeOptions.value = [
        { value: "SPECIFIC_USER", label: "指定用户" },
        { value: "ROLE_BASED", label: "基于角色" }
      ];
    });
}
```

### 2. 类型值不匹配
如果遇到未知的审批人类型值，界面会优雅降级：
- 配置项不显示（v-if 条件不满足）
- 不会影响其他功能正常使用

## 总结

### ✅ 已完成的修改
1. **条件判断**: 所有 v-if 条件从数字值改为字符串值
2. **默认值**: 添加步骤时的默认审批人类型改为字符串
3. **保存逻辑**: 序列化配置时的类型判断改为字符串
4. **数据加载**: 已集成字典API获取审批人类型选项

### ✅ 保持不变的部分
1. **数据回显**: approverConfig 解析逻辑无需修改
2. **表单验证**: 验证规则无需调整
3. **UI交互**: 用户操作流程保持一致

### ✅ 技术优势
1. **动态配置**: 审批人类型可通过字典管理
2. **语义化**: 使用有意义的字符串值替代数字
3. **扩展性**: 支持添加新的审批人类型
4. **一致性**: 与系统其他字典数据保持一致

现在审批流程配置功能完全基于字典数据，实现了真正的动态配置管理！
